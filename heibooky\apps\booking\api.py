import logging
from datetime import date, datetime, timedelta
from decimal import Decimal, InvalidOperation
from typing import Any, Dict, List, Optional, Tuple

from apps.booking.models import Booking, Customer, Reservation
from apps.stay.models import Property
from django.db import transaction
from django.db.utils import IntegrityError
from django.utils import timezone
from services.notification.handlers import ReservationNotificationHandler

logger = logging.getLogger(__name__)


class ReservationChangeTracker:
    """Tracks changes in reservation details"""

    TRACKED_FIELDS = {
        "checkin_date": "Check-in",
        "checkout_date": "Check-out",
        "total_price": "Total Price",
        "deposit": "Deposit",
        "number_of_guests": "Number of Guests",
        "number_of_adults": "Number of Adults",
        "number_of_children": "Number of Children",
        "number_of_infants": "Number of Infants",
        "payment_type": "Payment Type",
        "guest_name": "Guest Name",
    }

    @staticmethod
    def detect_changes(
        old_reservation: Reservation, new_data: Dict[str, Any]
    ) -> Dict[str, Tuple[Any, Any]]:
        """
        Detect changes between existing reservation and new data
        Returns dict of changed fields with their old and new values
        """
        changes = {}

        for field, display_name in ReservationChangeTracker.TRACKED_FIELDS.items():
            old_value = getattr(old_reservation, field)
            new_value = new_data.get(field)

            # Handle decimal fields
            if isinstance(old_value, Decimal):
                new_value = Decimal(str(to_float(new_value, field)))

            if new_value is not None and old_value != new_value:
                changes[display_name] = (old_value, new_value)

        return changes


def validate_decimal(
    value: Any, field_name: str, max_value: float = 99999999.99
) -> float:
    """Validate decimal values to prevent numeric field overflow."""
    try:
        float_val = float(value) if value not in (None, "") else 0.0
        if float_val > max_value:
            logger.warning(
                f"Value too large for {field_name}: {float_val}. Capping at {max_value}"
            )
            return round(max_value, 2)
        return round(float_val, 2)
    except (ValueError, TypeError):
        logger.error(f"Invalid value for {field_name}: {value}")
        return 0.00


def to_json_field(value: Any, field_name: str = "unnamed field") -> dict:
    """
    Convert a value to a dictionary for JSON fields.
    Handles None, empty lists, and other non-dict values.
    """
    if value is None or value == []:
        return {}
    if isinstance(value, dict):
        return value
    if isinstance(value, (list, tuple)) and len(value) == 0:
        return {}

    logger.warning(
        f"Unexpected type for JSON field {field_name}: {type(value)}. Converting to empty dict."
    )
    return {}


def to_float(value: Any, field_name: str = "unnamed field") -> float:
    """Convert a value to float with overflow protection."""
    return validate_decimal(value, field_name)


def to_int(value: Any) -> int:
    """Convert a value to integer, returning 0 if the value is None or empty."""
    return int(value) if value not in (None, "") else 0


def to_aware_datetime(value: Any) -> Optional[datetime]:
    """Convert a date string or naive datetime to a timezone-aware datetime; returns None if invalid without logging noisy errors."""
    if not value:
        return None

    # Already a datetime
    if isinstance(value, datetime):
        dt = value
    else:
        # Try common formats
        for fmt in ("%Y-%m-%d %H:%M:%S", "%Y-%m-%dT%H:%M:%S", "%Y-%m-%d"):
            try:
                dt = datetime.strptime(str(value), fmt)
                break
            except ValueError:
                dt = None
        if dt is None:
            return None

    if timezone.is_naive(dt):
        return timezone.make_aware(dt, timezone.get_current_timezone())
    return dt


def to_date(value: Any) -> Optional[datetime.date]:
    """Convert a value to a date object. Accepts YYYY-MM-DD strings or datetimes; returns None if invalid without logging noisy errors."""
    if not value:
        return None
    if isinstance(value, datetime):
        return value.date()
    # Try YYYY-MM-DD first
    try:
        return datetime.strptime(str(value), "%Y-%m-%d").date()
    except ValueError:
        # Try full timestamp
        try:
            return datetime.strptime(str(value), "%Y-%m-%d %H:%M:%S").date()
        except ValueError:
            # Try ISO format without timezone
            try:
                return datetime.strptime(str(value), "%Y-%m-%dT%H:%M:%S").date()
            except ValueError:
                return None


def derive_stay_dates(
    room_data: Dict[str, Any],
) -> Tuple[Optional[date], Optional[date]]:
    """Derive and validate check-in and check-out dates from a room payload.

    - Prefer arrival_date/departure_date as YYYY-MM-DD or timestamp strings.
    - If missing, use first price.date as check-in and (last price.date + 1 day) as check-out.
    - If only checkout provided, infer checkin = checkout - 1 day.
    - If only checkin provided, infer checkout = checkin + 1 day.
    - If order invalid, attempt minimal corrections or return (None, None).

    Returns: (checkin_date, checkout_date) as date objects or (None, None).
    """
    chk_in = to_date(room_data.get("arrival_date"))
    chk_out = to_date(room_data.get("departure_date"))

    # Derive check-in from price array if missing
    if not chk_in:
        price_entries = room_data.get("price") or []
        if isinstance(price_entries, list) and price_entries:
            sorted_entries = sorted(
                [e for e in price_entries if isinstance(e, dict)],
                key=lambda e: (e.get("date") or ""),
            )
            if sorted_entries:
                first_date = to_date(sorted_entries[0].get("date"))
                if first_date:
                    chk_in = first_date

    # Derive checkout from price array if missing
    if not chk_out and chk_in:
        price_entries = room_data.get("price") or []
        try:
            if isinstance(price_entries, list) and price_entries:
                sorted_entries = sorted(
                    [e for e in price_entries if isinstance(e, dict)],
                    key=lambda e: (e.get("date") or ""),
                )
                if sorted_entries:
                    last_date = to_date(sorted_entries[-1].get("date"))
                    if last_date:
                        chk_out = last_date + timedelta(days=1)
        except Exception:
            pass

    # If only checkout exists
    if chk_out and not chk_in:
        chk_in = chk_out - timedelta(days=1)

    # If only checkin exists
    if chk_in and not chk_out:
        chk_out = chk_in + timedelta(days=1)

    # Validate order
    if chk_in and chk_out and chk_out <= chk_in:
        if chk_out == chk_in:
            chk_out = chk_in + timedelta(days=1)
        else:
            logger.warning("Checkout before checkin in derive_stay_dates - swapping")
            chk_in, chk_out = chk_out, chk_in
        if chk_out <= chk_in:
            logger.error("Invalid date order in derive_stay_dates")
            return None, None

    if not chk_in or not chk_out:
        return None, None

    return chk_in, chk_out


def calculate_heibooky_pricing(
    original_net_price: float, ota_commission_fixed: float = 0.0
) -> Dict[str, float]:
    """
    Calculate Heibooky pricing and commissions for non-Domorent properties.

    Commission and tax structure:
    - OTA Commission: Fixed amount (already provided by SU)
    - Heibooky Commission: 8% of total price
    - Payment Charge: 3.5% of total price
    - IVA: 22% of (OTA commission + Heibooky commission + Payment charge)
    - Owner Tax: 21% of total price
    - Total for Owner: Total price - all deductions

    Args:
        original_net_price: Original net price from SU API
        ota_commission_fixed: Fixed OTA commission amount (from SU API)

    Returns:
        Dictionary with calculated pricing values including commissions and net total for owner
    """
    try:
        price = Decimal(str(original_net_price))
        ota_commission = Decimal(str(ota_commission_fixed))

        # Commission rates
        heibooky_commission_rate = Decimal("0.08")  # 8% Heibooky
        payment_charge_rate = Decimal("0.035")  # 3.5% Payment charge
        iva_rate = Decimal("0.22")  # 22% IVA on commissions
        owner_tax_rate = Decimal("0.21")  # 21% Owner tax

        # Calculate commissions
        heibooky_commission = price * heibooky_commission_rate
        payment_charge = price * payment_charge_rate

        # Calculate IVA (22% of OTA + Heibooky + Payment charges)
        total_commissions_for_iva = (
            ota_commission + heibooky_commission + payment_charge
        )
        iva = total_commissions_for_iva * iva_rate

        # Calculate owner tax (21% of total price)
        owner_tax = price * owner_tax_rate

        # Calculate total deductions
        total_deductions = (
            ota_commission + heibooky_commission + payment_charge + iva + owner_tax
        )

        # Net total for owner (what owner receives after all deductions)
        net_total_for_owner = price - total_deductions

        # For Heibooky properties:
        # - Customer pays the original net_price (this becomes total_price)
        # - All deductions come from this amount
        # - Owner receives what's left after deductions
        total_price = float(price)  # Customer pays the full net price

        # Total tax includes IVA and owner tax
        total_tax = float(iva + owner_tax)

        # Total commission includes all commission-related charges
        total_commission = float(ota_commission + heibooky_commission + payment_charge)

        return {
            "total_price": total_price,
            "total_tax": total_tax,
            "commission_amount": total_commission,
            "ota_commission": float(ota_commission),
            "heibooky_commission": float(heibooky_commission),
            "payment_charge": float(payment_charge),
            "iva": float(iva),
            "owner_tax": float(owner_tax),
            "net_total_for_owner": float(net_total_for_owner),
        }

    except (ValueError, TypeError, InvalidOperation) as e:
        logger.error(f"Error calculating Heibooky pricing: {str(e)}")
        # Return original values if calculation fails
        return {
            "total_price": original_net_price,
            "total_tax": 0.0,
            "commission_amount": float(ota_commission_fixed),
            "ota_commission": float(ota_commission_fixed),
            "heibooky_commission": 0.0,
            "payment_charge": 0.0,
            "iva": 0.0,
            "owner_tax": 0.0,
            "net_total_for_owner": original_net_price,
        }


def calculate_domorent_pricing(
    original_net_price: float,
    ota_commission_absolute: float = 27.0,
    property_cleaning_cost: float = 80.0,
    property_domorent_percentage: float = 25.0,
) -> Dict[str, float]:
    """
    Calculate Domorent pricing for properties where is_domorent = True.

    Updated calculation structure:
    - Base price: €400
    - OTA fee: €60
    - Cleaning fee: €80
    - Domorent commission: 25%*(400-80) = €80
    - Payment fee: 1.5%*400 = €6
    - VAT: 22%*(60+80+80+6) = €49.72
    - Subtotal: 400-60-80-80-6-49.72 = €124.28
    - Cedolare Dry Tax: 21% * 124.28 = €26.10
    - Revenue Stamp: €2
    - Final owner receives: 124.28 - 26.10 - 2 = €96.18

    Args:
        original_net_price: Original net price from SU API
        ota_commission_absolute: Absolute OTA commission value (varies by agreement)
        property_cleaning_cost: Property-specific cleaning cost
        property_domorent_percentage: Property-specific Domorent percentage

    Returns:
        Dictionary with calculated pricing values
    """
    try:
        price = Decimal(str(original_net_price))
        ota_fee = Decimal(str(ota_commission_absolute))
        cleaning_cost = Decimal(str(property_cleaning_cost))
        domorent_rate = Decimal(str(property_domorent_percentage)) / Decimal(
            "100"
        )  # Convert percentage to decimal

        # Fixed costs and rates for Domorent properties
        payment_rate = Decimal("0.015")  # 1.5% payment charge
        vat_rate = Decimal("0.22")  # 22% VAT on total costs
        cedolare_tax_rate = Decimal("0.21")  # 21% Cedolare Dry Tax
        revenue_stamp = Decimal("2.00")  # Fixed €2 revenue stamp

        # Calculate taxable amount for Domorent (Price - Cleaning Cost)
        taxable_domorent = price - cleaning_cost

        # Calculate Domorent commission (percentage of taxable amount)
        domorent_commission = domorent_rate * taxable_domorent

        # Calculate payment charge (1.5% of total price)
        payment_charge = payment_rate * price

        # Calculate VAT (22% of OTA + Cleaning + Domorent + Payment charges)
        total_costs_for_vat = (
            ota_fee + cleaning_cost + domorent_commission + payment_charge
        )
        vat = vat_rate * total_costs_for_vat

        # Calculate initial deductions (before owner taxes)
        initial_deductions = (
            ota_fee + cleaning_cost + domorent_commission + payment_charge + vat
        )

        # Calculate subtotal after initial deductions
        subtotal_after_initial = price - initial_deductions

        # Calculate Cedolare Dry Tax (21% of the subtotal)
        cedolare_tax = cedolare_tax_rate * subtotal_after_initial

        # Calculate final owner net transfer (subtotal - cedolare tax - revenue stamp)
        owner_net_transfer = subtotal_after_initial - cedolare_tax - revenue_stamp

        # For Domorent properties:
        # - Customer pays the original net_price (this becomes total_price)
        # - All costs are deducted from this amount
        # - Owner receives what's left after all deductions
        total_price = float(price)  # Customer pays the full net price
        total_tax = float(vat + cedolare_tax)  # VAT + Cedolare Tax

        # Total commission includes all non-tax charges
        commission_amount = float(ota_fee + domorent_commission + payment_charge)

        # Total deductions include everything except revenue stamp (which is separate)
        total_deductions = initial_deductions + cedolare_tax + revenue_stamp

        return {
            "total_price": total_price,
            "total_tax": total_tax,
            "commission_amount": commission_amount,
            "ota_commission": float(ota_fee),
            "domorent_commission": float(domorent_commission),
            "cleaning_cost": float(cleaning_cost),
            "payment_charge": float(payment_charge),
            "iva": float(vat),  # Keep iva for backward compatibility
            "vat": float(vat),
            "cedolare_tax": float(cedolare_tax),
            "revenue_stamp": float(revenue_stamp),
            "subtotal_after_initial": float(subtotal_after_initial),
            "owner_net_transfer": float(owner_net_transfer),
            "taxable_domorent": float(taxable_domorent),
            "total_deductions": float(total_deductions),
        }

    except (ValueError, TypeError, InvalidOperation) as e:
        logger.error(f"Error calculating Domorent pricing: {str(e)}")
        # Return original values if calculation fails
        return {
            "total_price": original_net_price,
            "total_tax": 0.0,
            "commission_amount": float(ota_commission_absolute),
            "ota_commission": float(ota_commission_absolute),
            "domorent_commission": 0.0,
            "cleaning_cost": float(property_cleaning_cost),
            "payment_charge": 0.0,
            "iva": 0.0,
            "vat": 0.0,
            "cedolare_tax": 0.0,
            "revenue_stamp": 2.0,
            "subtotal_after_initial": original_net_price,
            "owner_net_transfer": original_net_price,
            "taxable_domorent": 0.0,
            "total_deductions": 0.0,
        }


def process_push_reservation(reservations: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Process a single reservation from SU's push notification system.

    This function is designed to handle individual reservations pushed by SU's Channel Manager
    following their Push API method. It processes one reservation at a time and handles
    all the necessary database operations and notifications.

    Args:
        reservation_data: Dictionary containing reservation data from SU push notification

    Returns:
        Dictionary with processing status and details
    """
    logger.info(f"Processing push reservation: {reservations[0].get('id', 'unknown')}")

    try:
        if not reservations:
            logger.warning("No reservations to process")
            return {"status": "warning", "message": "No reservations provided"}

        reservation_data = reservations[0]
        hotel_id = reservation_data.get("hotel_id")

        try:
            property = Property.objects.get(hotel_id=hotel_id)
            logger.info(f"Found property {property.name} for hotel_id {hotel_id}")
        except Property.DoesNotExist:
            logger.error(f"Property with hotel_id {hotel_id} does not exist")
            return {
                "status": "error",
                "message": f"Property with hotel_id {hotel_id} not found in database",
            }

        affiliation = reservation_data.get("affiliation", {})

        with transaction.atomic():
            # Process customer data
            customer_data = reservation_data.get("customer", {})
            rooms = reservation_data.get("rooms", [])

            customer, _ = Customer.objects.update_or_create(
                email=customer_data["email"],
                defaults={
                    "first_name": customer_data.get("first_name"),
                    "last_name": customer_data.get("last_name"),
                    "telephone": customer_data.get("telephone"),
                    "address": customer_data.get("address"),
                    "city": customer_data.get("city"),
                    "state": customer_data.get("state"),
                    "country": customer_data.get("countrycode"),
                    "zip_code": customer_data.get("zip"),
                },
            )

            for room_data in rooms:
                # Check for existing reservation to track changes
                existing_reservation = Reservation.objects.filter(
                    id=reservation_data["id"]
                ).first()
                # Prepare reservation data
                original_total_tax = to_float(room_data.get("totaltax"), "total_tax")
                original_commission = to_float(
                    reservation_data.get("commissionamount"), "commission_amount"
                )
                original_net_price = to_float(room_data.get("totalprice"), "net_price")

                # Apply Domorent pricing logic if property is Domorent and reservation is from SU API
                if property.is_domorent:
                    logger.info(
                        f"Applying Domorent pricing for reservation {reservation_data['id']}"
                    )
                    # For Domorent, we use the new pricing structure with property-specific settings
                    # OTA commission is provided from SU, cleaning cost and percentage from property settings
                    domorent_pricing = calculate_domorent_pricing(
                        original_net_price=original_net_price,
                        ota_commission_absolute=original_commission,
                        property_cleaning_cost=float(property.cleaning_cost),
                        property_domorent_percentage=float(
                            property.domorent_percentage
                        ),
                    )

                    # Use calculated Domorent pricing
                    calculated_total_price = domorent_pricing["total_price"]
                    calculated_total_tax = domorent_pricing["total_tax"]
                    calculated_commission = domorent_pricing["commission_amount"]

                    logger.info(
                        f"Domorent pricing applied - Original: net={original_net_price}, tax={original_total_tax}, "
                        f"commission={original_commission} | "
                        f"Calculated: total={calculated_total_price}, tax={calculated_total_tax}, "
                        f"commission={calculated_commission}, owner_receives={domorent_pricing['owner_net_transfer']}"
                    )
                else:
                    # Apply Heibooky pricing for non-Domorent properties
                    logger.info(
                        f"Applying Heibooky pricing for reservation {reservation_data['id']}"
                    )
                    heibooky_pricing = calculate_heibooky_pricing(
                        original_net_price, original_commission
                    )

                    # Use calculated Heibooky pricing
                    calculated_total_price = heibooky_pricing["total_price"]
                    calculated_total_tax = heibooky_pricing["total_tax"]
                    calculated_commission = heibooky_pricing["commission_amount"]

                    logger.info(
                        f"Heibooky pricing applied - Original: net={original_net_price}, tax={original_total_tax}, "
                        f"commission={original_commission} | "
                        f"Calculated: total={calculated_total_price}, tax={calculated_total_tax}, "
                        f"commission={calculated_commission}, net_for_owner={heibooky_pricing['net_total_for_owner']}"
                    )

                # Determine the total_price based on property type
                if property.is_domorent:
                    # For Domorent: total_price is what owner receives after all deductions
                    final_total_price = domorent_pricing["owner_net_transfer"]
                else:
                    # For Heibooky: total_price is what owner receives after all deductions
                    final_total_price = heibooky_pricing["net_total_for_owner"]

                # Derive and validate stay dates using helper
                chk_in, chk_out = derive_stay_dates(room_data)
                if not chk_in or not chk_out:
                    logger.error(
                        f"Missing or invalid arrival/departure dates for reservation {reservation_data.get('id')} room {room_data.get('id')}; skipping room"
                    )
                    continue

                reservation_defaults = {
                    "guest_name": room_data.get("guest_name"),
                    "booked_at": to_aware_datetime(reservation_data.get("booked_at")),
                    # Reservation model stores dates (DateField); keep these as dates
                    "checkin_date": chk_in,
                    "checkout_date": chk_out,
                    "net_price": original_net_price,  # Original amount from SU API (what customer pays)
                    "total_price": final_total_price,  # Amount after deductions (what owner receives)
                    "total_tax": calculated_total_tax,  # Use calculated tax
                    "deposit": to_float(reservation_data.get("deposit"), "deposit"),
                    "commission_amount": calculated_commission,  # Use calculated commission
                    "payment_due": to_float(
                        reservation_data.get("paymentdue"), "payment_due"
                    ),
                    "cancellation_fee": to_float(
                        reservation_data.get("cancellation_fee"), "cancellation_fee"
                    ),
                    "reservation_notif_id": reservation_data.get(
                        "reservation_notif_id"
                    ),
                    "payment_type": reservation_data.get("paymenttype"),
                    "remarks": customer_data.get("remarks"),
                    "addons": to_json_field(room_data.get("addons"), "addons"),
                    "taxes": to_json_field(room_data.get("taxes"), "taxes"),
                    "number_of_infants": to_int(
                        reservation_data.get("numberofinfants")
                    ),
                    "modified_at": to_aware_datetime(
                        reservation_data.get("modified_at")
                    ),
                    "number_of_guests": to_int(room_data.get("numberofguests")),
                    "number_of_adults": to_int(room_data.get("numberofadults")),
                    "number_of_children": to_int(room_data.get("numberofchildren")),
                    "processed_at": to_aware_datetime(
                        reservation_data.get("processed_at")
                    ),
                }

                # Add detailed commission breakdown for non-Domorent properties
                if not property.is_domorent:
                    reservation_defaults.update(
                        {
                            "ota_commission": heibooky_pricing["ota_commission"],
                            "heibooky_commission": heibooky_pricing[
                                "heibooky_commission"
                            ],
                            "payment_charge": heibooky_pricing["payment_charge"],
                            "iva_amount": heibooky_pricing["iva"],
                            "owner_tax": heibooky_pricing["owner_tax"],
                            "net_total_for_owner": heibooky_pricing[
                                "net_total_for_owner"
                            ],
                        }
                    )
                else:
                    # Add Domorent-specific breakdown
                    reservation_defaults.update(
                        {
                            "ota_commission": domorent_pricing["ota_commission"],
                            "payment_charge": domorent_pricing["payment_charge"],
                            "iva_amount": domorent_pricing["iva"],
                            "owner_tax": domorent_pricing[
                                "cedolare_tax"
                            ],  # Store cedolare tax as owner_tax
                            "cleaning_cost": domorent_pricing["cleaning_cost"],
                            "taxable_domorent": domorent_pricing["taxable_domorent"],
                            "tot_platform_commission": domorent_pricing.get(
                                "tot", 0.0
                            ),  # Use get() for safety
                            "owner_net_transfer": domorent_pricing[
                                "owner_net_transfer"
                            ],
                            # Store domorent commission in heibooky_commission field for compatibility
                            "heibooky_commission": domorent_pricing[
                                "domorent_commission"
                            ],
                            # Note: cedolare_tax, revenue_stamp, subtotal_after_initial are not stored
                            # in the database as they are calculated values available in the pricing result
                        }
                    )

                # Track changes if reservation exists
                changes = {}
                if existing_reservation:
                    changes = ReservationChangeTracker.detect_changes(
                        existing_reservation, reservation_defaults
                    )

                # Update or create reservation
                reservation, is_new = Reservation.objects.update_or_create(
                    id=reservation_data["id"], defaults=reservation_defaults
                )

                # Process booking with duplicate handling
                try:
                    with transaction.atomic():
                        # Try to get existing booking first
                        existing_booking = (
                            Booking.objects.filter(
                                reservation_data__id=reservation_data["id"]
                            )
                            .select_related("property", "customer")
                            .first()
                        )

                        status = room_data.get("roomstaystatus")
                        # Derive and validate dates using helper
                        b_checkin, b_checkout = derive_stay_dates(room_data)
                        if not b_checkin or not b_checkout:
                            logger.error(
                                f"Skipping booking creation due to missing dates for reservation {reservation_data.get('id')} room {room_data.get('id')}"
                            )
                            continue

                        booking_defaults = {
                            "property": property,
                            "customer": customer,
                            "is_manual": False,
                            "status": status,
                            # Booking model stores DateField; assign dates
                            "checkin_date": b_checkin,
                            "checkout_date": b_checkout,
                            "channel_code": (
                                int(affiliation.get("OTA_Code"))
                                if str(affiliation.get("OTA_Code") or "").isdigit()
                                else None
                            ),
                            "booking_date": to_aware_datetime(
                                reservation_data.get("booked_at")
                            ),
                        }
                        if existing_booking:
                            # Update existing booking
                            for key, value in booking_defaults.items():
                                setattr(existing_booking, key, value)
                            existing_booking.save()
                            booking = existing_booking
                            is_new_booking = False
                        else:
                            # Create new booking
                            booking = Booking.objects.create(
                                reservation_data=reservation, **booking_defaults
                            )
                            is_new_booking = True

                        # Handle notifications
                        handler = ReservationNotificationHandler(property, customer)

                        if (
                            is_new_booking
                            or status == Booking.Status.NEW
                            or status == Booking.Status.REQUEST
                        ):
                            handler.new_reservation_handler(booking)
                        elif changes and status == Booking.Status.MODIFIED:
                            handler.modified_reservation_handler(booking, changes)
                        elif status == Booking.Status.CANCELLED:
                            handler.cancelled_reservation_handler(booking)

                except IntegrityError as e:
                    logger.error(
                        f"Integrity error processing booking for reservation {reservation_data['id']}: {str(e)}",
                        exc_info=True,
                    )
                    continue
                except Exception as e:
                    logger.error(
                        f"Error processing booking for reservation {reservation_data['id']}: {str(e)}",
                        exc_info=True,
                    )
                    continue

                # acknowledge the reservation
                # reservation_notification(reservation)

        logger.info("Completed reservation fetch task successfully")
        return {"status": "success", "message": reservations[0]}

    except Exception as e:
        logger.error(f"Error processing property reservation: {hotel_id}: {str(e)}")
        return {"status": "error", "message": str(e)}
